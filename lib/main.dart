import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:survival_guide_earthquake/app_state.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppState().init();

  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late FlutterI18nDelegate flutterI18nDelegate;

  @override
  void initState() {
    flutterI18nDelegate = FlutterI18nDelegate(
      translationLoader: FileTranslationLoader(
        basePath: 'assets/i18n',
        forcedLocale: AppState().locale,
      ),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: FlutterI18n.translate(context, 'app_title'),
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Color.fromARGB(255, 205, 218, 168),
        ),
      ),
      home: const MyHomePage(title: '지진 생존 가이드'),
      localizationsDelegates: [flutterI18nDelegate],
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,

        title: Text(widget.title),
        actions: [
          IconButton(
            icon: Icon(Icons.language),
            onPressed: () {
              // Handle language change
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            ListTile(
              title: Text('평소 대비 요령', style: TextStyle(fontSize: 20)),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // Handle item tap
              },
            ),
            ListTile(
              title: Text('지진 발생 시 대비 요령', style: TextStyle(fontSize: 20)),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // Handle item tap
              },
            ),
            ListTile(
              title: Text('지진 대피 후 대비 요령', style: TextStyle(fontSize: 20)),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // Handle item tap
              },
            ),
          ],
        ),
      ),
    );
  }
}
